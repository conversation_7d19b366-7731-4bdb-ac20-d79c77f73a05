Metadata-Version: 2.4
Name: descent
Version: 0.2.5
Summary: First order optimization tools
Home-page: https://github.com/nirum/descent
Author: <PERSON><PERSON>
Author-email: <EMAIL>
License: MIT
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Science/Research
Classifier: Operating System :: OS Independent
Classifier: Topic :: Scientific/Engineering
Requires-Dist: six
Requires-Dist: numpy
Requires-Dist: toolz
Requires-Dist: scipy
Requires-Dist: multipledispatch
Requires-Dist: custom_inherit
Requires-Dist: tableprint
Provides-Extra: dev
Provides-Extra: test
Requires-Dist: coveralls; extra == "test"
Requires-Dist: pytest; extra == "test"
Requires-Dist: coverage; extra == "test"
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: home-page
Dynamic: license
Dynamic: provides-extra
Dynamic: requires-dist
Dynamic: summary


        The descent package contains tools for performing first order
        optimization of functions. That is, given the gradient of an
        objective you wish to minimize, descent provides algorithms for
        finding local minima of that function.
        
